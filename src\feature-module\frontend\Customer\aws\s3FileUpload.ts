import {
    S3Client,
    PutObjectCommand,
    DeleteObjectCommand,
  } from "@aws-sdk/client-s3";
  
  const s3 = new S3Client({
    region: import.meta.env.VITE_S3_BUCKET_REGION,
    credentials: {
      accessKeyId: import.meta.env.VITE_S3_ACCESS_KEY,
      secretAccessKey: import.meta.env.VITE_S3_SECRET_KEY,
    },
  });
  
  const S3_BUCKET = import.meta.env.VITE_S3_BUCKET_NAME;
  const CDN_URL = import.meta.env.VITE_S3_CDN_URL;
  const S3_BASE_URL = `https://${S3_BUCKET}.s3.${
    import.meta.env.VITE_S3_BUCKET_REGION
  }.amazonaws.com/`;
  
  export const uploadToS3 = async (file: File, folderName: string) => {
    if (!file) throw new Error("No file provided for upload");

    const key = `${folderName}/${file.name}-${Date.now()}`;

    const params = {
      Bucket: S3_BUCKET,
      Key: key,
      Body: await file.arrayBuffer(),
      ContentType: file.type,
    };

    try {
      const command = new PutObjectCommand(params);
      await s3.send(command);

      const s3Url = `${S3_BASE_URL}${key}`;
      const cdnUrl = s3Url.replace(S3_BASE_URL, CDN_URL);

      console.log("File uploaded successfully:", cdnUrl);
      return cdnUrl;
    } catch (error) {
      console.error("S3 Upload Error: ", error);
      throw error;
    }
  };

  // Enhanced function to upload and return image name with metadata
  export const uploadToS3WithImageName = async (file: File, folderName: string = 'review-images') => {
    if (!file) throw new Error("No file provided for upload");

    // Create unique image name with timestamp
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const cleanFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_'); // Sanitize filename
    const imageName = `${cleanFileName}-${timestamp}.${fileExtension}`;
    const key = `${folderName}/${imageName}`;

    const params = {
      Bucket: S3_BUCKET,
      Key: key,
      Body: await file.arrayBuffer(),
      ContentType: file.type,
      // Add metadata for better file management
      Metadata: {
        'original-name': file.name,
        'upload-timestamp': timestamp.toString(),
        'file-size': file.size.toString()
      }
    };

    try {
      const command = new PutObjectCommand(params);
      await s3.send(command);

      const s3Url = `${S3_BASE_URL}${key}`;
      const cdnUrl = s3Url.replace(S3_BASE_URL, CDN_URL);

      console.log("File uploaded successfully to S3:", {
        imageName,
        originalName: file.name,
        size: file.size,
        url: cdnUrl
      });

      return {
        imageName,        // Just the filename for database storage
        fullUrl: cdnUrl,  // Complete URL for immediate display
        key,              // S3 key for deletion
        originalName: file.name,
        size: file.size,
        timestamp
      };
    } catch (error) {
      console.error("S3 Upload Error: ", error);
      throw new Error(`Failed to upload ${file.name} to S3: ${error}`);
    }
  };

  // Helper function to construct URL from image name
  export const getImageUrlFromName = (imageName: string, folderName: string = 'review-images') => {
    if (!imageName) {
      console.warn('getImageUrlFromName: No image name provided');
      return '';
    }

    // Validate environment variables
    if (!S3_BUCKET) {
      console.error('getImageUrlFromName: S3_BUCKET is not defined');
      return '';
    }
    if (!CDN_URL) {
      console.error('getImageUrlFromName: CDN_URL is not defined');
      return '';
    }

    const key = `${folderName}/${imageName}`;
    const s3Url = `${S3_BASE_URL}${key}`;
    const cdnUrl = s3Url.replace(S3_BASE_URL, CDN_URL);

    console.log('getImageUrlFromName:', {
      imageName,
      folderName,
      key,
      s3Url,
      cdnUrl,
      S3_BUCKET,
      CDN_URL: CDN_URL?.substring(0, 50) + '...'
    });

    // Validate final URL
    if (!cdnUrl.startsWith('http')) {
      console.error('getImageUrlFromName: Generated URL is invalid:', cdnUrl);
      return '';
    }

    return cdnUrl;
  };

  // Debug function to check S3 configuration
  export const debugS3Configuration = () => {
    console.log('=== S3 Configuration Debug ===');
    console.log('S3_BUCKET:', S3_BUCKET);
    console.log('CDN_URL:', CDN_URL);
    console.log('S3_BASE_URL:', S3_BASE_URL);
    console.log('VITE_S3_BUCKET_REGION:', import.meta.env.VITE_S3_BUCKET_REGION);
    console.log('Environment variables available:', {
      VITE_S3_BUCKET_NAME: !!import.meta.env.VITE_S3_BUCKET_NAME,
      VITE_S3_CDN_URL: !!import.meta.env.VITE_S3_CDN_URL,
      VITE_S3_BUCKET_REGION: !!import.meta.env.VITE_S3_BUCKET_REGION,
      VITE_S3_ACCESS_KEY: !!import.meta.env.VITE_S3_ACCESS_KEY,
      VITE_S3_SECRET_KEY: !!import.meta.env.VITE_S3_SECRET_KEY
    });
    console.log('================================');
  };

  // Helper function to get multiple image URLs from names array
  export const getImageUrlsFromNames = (imageNames: string[], folderName: string = 'review-images') => {
    if (!Array.isArray(imageNames)) return [];

    return imageNames
      .filter(name => name && typeof name === 'string')
      .map(name => getImageUrlFromName(name, folderName));
  };
  
  export const deleteFromS3 = async (fileUrl: string) => {
    if (!fileUrl) throw new Error("No file URL provided for deletion");

    const fileKey = fileUrl.replace(CDN_URL, "");

    const params = {
      Bucket: S3_BUCKET,
      Key: fileKey,
    };

    try {
      const command = new DeleteObjectCommand(params);
      await s3.send(command);

      console.log("File deleted successfully:", fileUrl);
      return true;
    } catch (error) {
      console.error("S3 Delete Error: ", error);
      throw error;
    }
  };

  // Enhanced function to delete by image name
  export const deleteFromS3ByImageName = async (imageName: string, folderName: string = 'review-images') => {
    if (!imageName || typeof imageName !== 'string') {
      throw new Error("Valid image name is required for deletion");
    }

    const fileKey = `${folderName}/${imageName}`;

    const params = {
      Bucket: S3_BUCKET,
      Key: fileKey,
    };

    try {
      const command = new DeleteObjectCommand(params);
      await s3.send(command);

      console.log("File deleted successfully from S3:", {
        imageName,
        key: fileKey
      });
      return true;
    } catch (error) {
      console.error("S3 Delete Error: ", error);
      throw new Error(`Failed to delete ${imageName} from S3: ${error}`);
    }
  };

  // Helper function to delete multiple images by names
  export const deleteMultipleFromS3ByImageNames = async (imageNames: string[], folderName: string = 'review-images') => {
    if (!Array.isArray(imageNames) || imageNames.length === 0) {
      return { success: [] as string[], failed: [] as Array<{imageName: string, error: string}> };
    }

    const results = {
      success: [] as string[],
      failed: [] as Array<{imageName: string, error: string}>
    };

    // Process deletions in parallel
    const deletePromises = imageNames.map(async (imageName) => {
      try {
        await deleteFromS3ByImageName(imageName, folderName);
        results.success.push(imageName);
      } catch (error) {
        results.failed.push({
          imageName,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    await Promise.all(deletePromises);
    return results;
  };

  // Profile picture specific functions
  export const uploadProfilePictureToS3 = async (file: File, userId: string) => {
    if (!file) throw new Error("No file provided for profile picture upload");
    if (!userId) throw new Error("User ID is required for profile picture upload");

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error("Invalid file type. Only JPEG, PNG, and WebP images are allowed.");
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new Error("File size too large. Maximum size is 5MB.");
    }

    // Create unique image name for profile picture
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const imageName = `profile-${userId}-${timestamp}.${fileExtension}`;
    const key = `profile-images/${imageName}`;

    const params = {
      Bucket: S3_BUCKET,
      Key: key,
      Body: await file.arrayBuffer(),
      ContentType: file.type,
      Metadata: {
        'user-id': userId,
        'original-name': file.name,
        'upload-timestamp': timestamp.toString(),
        'file-size': file.size.toString(),
        'image-type': 'profile-picture'
      }
    };

    try {
      const command = new PutObjectCommand(params);
      await s3.send(command);

      const s3Url = `${S3_BASE_URL}${key}`;
      const cdnUrl = s3Url.replace(S3_BASE_URL, CDN_URL);

      console.log("Profile picture uploaded successfully to S3:", {
        imageName,
        userId,
        originalName: file.name,
        size: file.size,
        url: cdnUrl
      });

      return {
        imageName,        // Just the filename for database storage
        fullUrl: cdnUrl,  // Complete URL for immediate display
        key,              // S3 key for deletion
        originalName: file.name,
        size: file.size,
        timestamp,
        userId
      };
    } catch (error) {
      console.error("S3 Profile Picture Upload Error: ", error);
      throw new Error(`Failed to upload profile picture to S3: ${error}`);
    }
  };

  // Helper function to get profile picture URL from image name
  export const getProfilePictureUrl = (imageName: string) => {
    if (!imageName) return '';
    return getImageUrlFromName(imageName, 'profile-images');
  };

  // Helper function to delete old profile picture
  export const deleteProfilePictureFromS3 = async (imageName: string) => {
    if (!imageName) return true; // Nothing to delete

    try {
      await deleteFromS3ByImageName(imageName, 'profile-images');
      console.log("Old profile picture deleted successfully:", imageName);
      return true;
    } catch (error) {
      console.error("Error deleting old profile picture:", error);
      // Don't throw error for deletion failures to avoid blocking new uploads
      return false;
    }
  };